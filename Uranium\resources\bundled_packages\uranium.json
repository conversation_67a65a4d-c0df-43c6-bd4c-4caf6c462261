{"ConsoleLogger": {"package_info": {"package_id": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "<PERSON><PERSON><PERSON>", "description": "Outputs log information to the console.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "OBJReader": {"package_info": {"package_id": "OBJR<PERSON>er", "package_type": "plugin", "display_name": "Wavefront OBJ Reader", "description": "Makes it possible to read Wavefront OBJ files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "OBJWriter": {"package_info": {"package_id": "OBJWriter", "package_type": "plugin", "display_name": "Wavefront OBJ Writer", "description": "Makes it possible to write Wavefront OBJ files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "STLReader": {"package_info": {"package_id": "STLReader", "package_type": "plugin", "display_name": "STL Reader", "description": "Provides support for reading STL files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "STLWriter": {"package_info": {"package_id": "STLWriter", "package_type": "plugin", "display_name": "STL Writer", "description": "Provides support for writing STL files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "FileLogger": {"package_info": {"package_id": "FileLogger", "package_type": "plugin", "display_name": "File Logger", "description": "Outputs log information to a file in your settings folder.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "LocalContainerProvider": {"package_info": {"package_id": "LocalContainerProvider", "package_type": "plugin", "display_name": "Local Container Provider", "description": "Provides built-in setting containers that come with the installation of the application.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "LocalFileOutputDevice": {"package_info": {"package_id": "LocalFileOutputDevice", "package_type": "plugin", "display_name": "Local File Output Device", "description": "Enables saving to local files.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "CameraTool": {"package_info": {"package_id": "CameraTool", "package_type": "plugin", "display_name": "Camera Tool", "description": "Provides the tool to manipulate the camera.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "MirrorTool": {"package_info": {"package_id": "MirrorTool", "package_type": "plugin", "display_name": "Mirror Tool", "description": "Provides the Mirror tool.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "RotateTool": {"package_info": {"package_id": "RotateTool", "package_type": "plugin", "display_name": "Rotate Tool", "description": "Provides the Rotate tool.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "ScaleTool": {"package_info": {"package_id": "ScaleTool", "package_type": "plugin", "display_name": "Scale Tool", "description": "Provides the Scale tool.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SelectionTool": {"package_info": {"package_id": "SelectionTool", "package_type": "plugin", "display_name": "Selection Tool", "description": "Provides the Selection tool.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "TranslateTool": {"package_info": {"package_id": "TranslateTool", "package_type": "plugin", "display_name": "Move Tool", "description": "Provides the Move tool.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "UpdateChecker": {"package_info": {"package_id": "Update<PERSON><PERSON><PERSON>", "package_type": "plugin", "display_name": "Update Checker", "description": "Checks for updates of the software.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}, "SimpleView": {"package_info": {"package_id": "SimpleView", "package_type": "plugin", "display_name": "Simple View", "description": "Provides a simple solid mesh view.", "package_version": "1.0.1", "sdk_version": "8.6.0", "website": "https://ultimaker.com", "author": {"author_id": "UltimakerPackages", "display_name": "UltiMaker", "email": "<EMAIL>", "website": "https://ultimaker.com"}}}}