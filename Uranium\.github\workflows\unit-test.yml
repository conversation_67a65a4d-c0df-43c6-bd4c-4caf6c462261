name: unit-test

on:
  push:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'UM/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - '[0-9]+.[0-9]+'

  pull_request:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'UM/**'
      - 'icons/**'
      - 'tests/**'
      - '.github/workflows/unit-test.yml'
      - 'requirements*.txt'
      - 'conanfile.py'
      - 'conandata.yml'
      - '*.jinja'
    branches:
      - main
      - '[0-9]+.[0-9]+'

jobs:
  testing:
    name: Run unit tests
    uses: ultimaker/cura-workflows/.github/workflows/unit-test.yml@main
    with:
      test_use_pytest: true
