# Cura 并行开发环境设置指南

本指南将帮助您设置Cura、CuraEngine和Uranium的并行开发环境，特别针对ARM64 Windows兼容性问题进行了优化。

## 环境要求

- Python 3.11.7 (推荐)
- Git
- PyCharm (推荐IDE)
- Windows 10/11 (ARM64 或 x64)

## 快速设置

### Windows用户

```bash
# 运行自动设置脚本
setup_dev_environment.bat
```

### Linux/macOS用户

```bash
# 运行自动设置脚本
./setup_dev_environment.sh
```

## 手动设置步骤

如果自动脚本失败，可以按照以下步骤手动设置：

### 1. 安装兼容的Conan版本

```bash
pip install "conan>=2.6.0,<2.7.0"
```

### 2. 设置Conan配置

```bash
conan profile detect --force
```

### 3. 设置editable包

```bash
# 添加Uranium为可编辑包
cd Uranium
conan editable add . --name=uranium --version=5.11.0-alpha.0
cd ..

# 添加CuraEngine为可编辑包
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0-alpha.0
cd ..
```

### 4. 安装Cura依赖并生成PyCharm配置

```bash
cd Cura
conan install . -g PyCharmRunEnv --build=missing -c tools.system.package_manager:mode=install -c tools.system.package_manager:sudo=True
```

## 兼容性优化

本环境针对ARM64 Windows兼容性问题进行了以下优化：

1. **Python版本回退**: 从3.12.2回退到3.11.7
2. **依赖版本固定**: 使用2024年12月前的稳定版本
   - certifi: 2024.8.30
   - setuptools: 75.1.0
   - requests: 2.31.0
   - urllib3: 2.0.7
   - keyring: 24.3.1
   - pyinstaller: 6.10.0
3. **Conan版本**: 使用2.6.0而非2.7.1

## PyCharm配置

设置完成后，PyCharm会自动生成运行配置：

1. 打开PyCharm
2. 导入Cura项目
3. 在运行配置中选择：
   - `cura`: 标准Cura运行
   - `cura_external_engine`: 使用外部引擎运行
   - 各种测试配置

## 开发工作流

### 修改Cura代码
直接在`Cura/`目录下修改代码，更改会立即生效。

### 修改Uranium代码
1. 在`Uranium/`目录下修改代码
2. 重启Cura即可看到更改

### 修改CuraEngine代码
1. 在`CuraEngine/`目录下修改代码
2. 重新编译CuraEngine：
   ```bash
   cd CuraEngine
   conan create . --build=missing
   ```
3. 重启Cura

## 故障排除

### 如果遇到ARM64兼容性问题
1. 确保使用的是修改后的依赖版本
2. 检查Python版本是否为3.11.7
3. 清理Conan缓存：`conan remove "*" -c`

### 如果PyCharm配置未生成
1. 确保使用了`-g PyCharmRunEnv`参数
2. 检查`.run_templates/`目录是否存在
3. 重新运行conan install命令

### 如果依赖安装失败
1. 检查网络连接
2. 尝试使用`--build=missing`强制构建
3. 清理Conan缓存后重试

## 验证安装

运行以下命令验证环境设置是否正确：

```bash
# 检查editable包
conan editable list

# 检查Cura是否能正常启动
cd Cura
python cura_app.py --help
```

## 更多信息

- [Cura官方开发文档](https://github.com/Ultimaker/Cura/wiki/Getting-Started)
- [Conan文档](https://docs.conan.io/)
- [PyCharm配置指南](https://www.jetbrains.com/help/pycharm/)
