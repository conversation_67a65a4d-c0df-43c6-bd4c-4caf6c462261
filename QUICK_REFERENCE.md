# Cura 开发环境快速参考

## 关键版本信息
- **Python**: 3.11.7 (不是3.12.2)
- **Conan**: 2.6.0 (不是2.7.1)
- **架构**: ARM64 Windows

## 关键文件修改摘要

### Cura/conanfile.py
```python
# 第22行
required_conan_version = ">=2.6.0"

# 第556行
self.requires("cpython/3.11.7")
```

### Cura/conandata.yml
```yaml
# 依赖版本回退
certifi: "2024.8.30"
setuptools: "75.1.0"
requests: "2.31.0"
urllib3: "2.0.7"
keyring: "24.3.1"
pyinstaller: "6.10.0"

# Conan版本
extra_dependencies:
  conan:
    version: "2.6.0"

# 主要依赖
requirements:
  - "cura_resources/5.10.0"
  - "cura_binary_data/5.10.0"
  - "fdm_materials/5.10.0"
  - "pysavitar/5.10.0"
```

### Uranium/conanfile.py
```python
# 第12行
required_conan_version = ">=2.6.0"
```

### CuraEngine/conanfile.py
```python
# 第13行
required_conan_version = ">=2.6.0"

# 第26-27行 (注释掉)
# python_requires = "sentrylibrary/1.0.0", "npmpackage/[>=1.0.0]"
# python_requires_extend = "sentrylibrary.SentryLibrary"

# 第56-58行 (注释掉)
# def init(self):
#     base = self.python_requires["sentrylibrary"].module.SentryLibrary
#     self.options.update(base.options, base.default_options)
```

## 快速设置命令

```bash
# 1. 安装兼容Conan版本
pip install "conan>=2.6.0,<2.7.0"

# 2. 配置Conan
conan profile detect --force

# 3. 设置editable包
cd Uranium && conan editable add . --name=uranium --version=5.11.0-alpha.0 && cd ..
cd CuraEngine && conan editable add . --name=curaengine --version=5.11.0-alpha.0 && cd ..

# 4. 安装Cura依赖
cd Cura && conan install . -g PyCharmRunEnv --build=missing
```

## 验证命令

```bash
# 检查Conan版本
conan --version  # 应该显示 2.6.0

# 检查editable包
conan editable list

# 测试Cura
cd Cura && python cura_app.py --help
```

## 故障排除

### 如果遇到版本冲突
```bash
conan remove "*" -c  # 清理缓存
```

### 如果editable包设置失败
```bash
conan editable remove uranium/5.11.0-alpha.0
conan editable remove curaengine/5.11.0-alpha.0
# 然后重新添加
```

### 如果依赖安装失败
- 检查网络连接
- 确保所有配置文件修改正确
- 使用 `--build=missing` 参数

## 成功标志
✅ `conan --version` 显示 2.6.0  
✅ `conan editable list` 显示两个包  
✅ `python cura_app.py --help` 正常运行  
✅ PyCharm中有运行配置  

## 重要提醒
⚠️ 必须使用修改后的版本号  
⚠️ ARM64 Windows需要特定的依赖版本  
⚠️ 不要升级到最新版本的依赖包  
⚠️ 保持Python 3.11.7，不要升级到3.12+
