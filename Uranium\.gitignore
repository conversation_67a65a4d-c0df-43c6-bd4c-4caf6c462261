# CMake
CMakeFiles/*
cmake_install.cmake
CMakeCache.txt
CPackSourceConfig.cmake

# CMake - Makefile generator
Makefile

# Build results
build
LC_MESSAGES
*.mo
*.qm
*.qmlc
*.qsb
resources/i18n/en_US
resources/i18n/x-test
resources/i18n/en_7S
.mypy_cache
.coverage*
htmlcov/

# JUnit
junit.xml
junit-pytest-*

# Different files
*.pyc
*kdev*
__pycache__
docs/html
*.lprof
*.log
*~
Uranium.e4p
_eric6project/Uranium.e4q
_eric6project/Uranium.e4t
.cache
*.kate-swp
*.bak

## Eclipse+PyDev incl. Mylyn
.project
.settings/org.eclipse.mylyn.tasks.ui.prefs
.pydevproject

## IntelliJ projects
.idea

#Test results.
tests/Settings/instances/machine_settings_with_overrides_test.cfg
tests/Settings/profiles/simple_machine_with_overrides_test.cfg
.pytest_cache/

#MacOS
.DS_Store
UM/.DS_Store
resources/.DS_Store

#Common plug-ins
plugins/UraniumExample*Plugin
/conanbuildinfo.txt
/graph_info.json
/venv/
