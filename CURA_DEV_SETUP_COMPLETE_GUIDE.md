# Cura 开发环境完整搭建指南

## 概述

本指南详细记录了在ARM64 Windows环境下搭建Cura、CuraEngine和Uranium并行开发环境的完整步骤，解决了ARM64兼容性问题。

## 环境信息

- **操作系统**: Windows 11 ARM64 (PD虚拟机)
- **硬件**: MacBook Pro M4 Pro
- **Python版本**: 3.11.7 (回退版本，解决兼容性)
- **Conan版本**: 2.6.0 (回退版本，解决兼容性)

## 问题分析

### 原始问题
1. ARM64 Windows与最新依赖包不兼容
2. Python 3.12.2在ARM64 Windows上存在兼容性问题
3. Conan 2.7.1要求过高，部分依赖包不支持
4. 多个Python包的最新版本未适配ARM64 Windows

### 解决方案
1. 回退Python版本到3.11.7
2. 回退Conan版本到2.6.0
3. 固定依赖包版本到2024年12月前的稳定版本
4. 使用editable模式配置并行开发环境

## 详细操作步骤

### 第一步：准备工作

1. **确保目录结构**
   ```
   Cura-Dev/
   ├── Cura/
   ├── CuraEngine/
   └── Uranium/
   ```

2. **检查Python版本**
   ```bash
   python --version  # 应该是 3.11.x
   ```

### 第二步：修改配置文件

#### 2.1 修改 Cura/conanfile.py

**关键修改1**: Python版本回退
```python
# 第556行，将：
self.requires("cpython/3.12.2")
# 改为：
self.requires("cpython/3.11.7")
```

**关键修改2**: Conan版本要求
```python
# 第22行，将：
required_conan_version = ">=2.7.0"
# 改为：
required_conan_version = ">=2.6.0"
```

#### 2.2 修改 Cura/conandata.yml

**关键修改1**: 依赖版本回退
```yaml
# 将以下依赖版本回退：
certifi: "2024.8.30"  # 从 2023.5.7
setuptools: "75.1.0"  # 从 75.6.0
requests: "2.31.0"    # 从 2.32.3
urllib3: "2.0.7"      # 从 2.2.3
keyring: "24.3.1"     # 从 25.5.0
pyinstaller: "6.10.0" # 从 6.11.1
```

**关键修改2**: Conan版本
```yaml
extra_dependencies:
  conan:
    version: "2.6.0"  # 从 2.7.1
```

**关键修改3**: 主要依赖包版本
```yaml
requirements:
  - "cura_resources/5.10.0"
  - "uranium/5.11.0-alpha.0@ultimaker/testing"
  - "curaengine/5.11.0-alpha.0@ultimaker/testing"
  - "cura_binary_data/5.10.0"
  - "fdm_materials/5.10.0"
  - "dulcificum/5.10.0"
  - "pysavitar/5.10.0"
  - "pynest2d/5.10.0"
```

#### 2.3 修改 Uranium/conanfile.py

```python
# 第12行，将：
required_conan_version = ">=2.7.0"
# 改为：
required_conan_version = ">=2.6.0"
```

#### 2.4 修改 CuraEngine/conanfile.py

**关键修改1**: Conan版本要求
```python
# 第13行，将：
required_conan_version = ">=2.7.0"
# 改为：
required_conan_version = ">=2.6.0"
```

**关键修改2**: 临时禁用Sentry依赖（解决兼容性问题）
```python
# 第26-27行，将：
python_requires = "sentrylibrary/1.0.0", "npmpackage/[>=1.0.0]"
python_requires_extend = "sentrylibrary.SentryLibrary"
# 改为：
# python_requires = "sentrylibrary/1.0.0", "npmpackage/[>=1.0.0]"
# python_requires_extend = "sentrylibrary.SentryLibrary"

# 第56-58行，将：
def init(self):
    base = self.python_requires["sentrylibrary"].module.SentryLibrary
    self.options.update(base.options, base.default_options)
# 改为：
# def init(self):
#     base = self.python_requires["sentrylibrary"].module.SentryLibrary
#     self.options.update(base.options, base.default_options)
```

### 第三步：安装兼容的Conan版本

```bash
pip install "conan>=2.6.0,<2.7.0"
```

### 第四步：配置Conan

```bash
conan profile detect --force
```

**验证配置**：确保输出显示 `arch=armv8`

### 第五步：设置Editable包

```bash
# 添加Uranium为editable包
cd Uranium
conan editable add . --name=uranium --version=5.11.0-alpha.0
cd ..

# 添加CuraEngine为editable包
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0-alpha.0
cd ..
```

**验证editable包**：
```bash
conan editable list
```

### 第六步：安装Cura依赖

```bash
cd Cura
conan install . -g PyCharmRunEnv --build=missing
```

## 验证安装

### 1. 检查editable包
```bash
conan editable list
```
应该显示：
```
uranium/5.11.0-alpha.0
    Path: C:\Mac\Home\Desktop\Cura-Dev\Uranium\conanfile.py
curaengine/5.11.0-alpha.0
    Path: C:\Mac\Home\Desktop\Cura-Dev\CuraEngine\conanfile.py
```

### 2. 检查Cura是否能启动
```bash
cd Cura
python cura_app.py --help
```

### 3. 检查PyCharm配置文件
确保在 `Cura/.idea/runConfigurations/` 目录下生成了运行配置文件。

## PyCharm配置

1. 打开PyCharm
2. 导入Cura项目
3. 运行配置应该自动可用：
   - `cura`: 标准运行
   - `cura_external_engine`: 外部引擎运行
   - 各种测试配置

## 开发工作流

### Cura代码修改
直接修改 `Cura/` 目录下的代码，重启即可生效。

### Uranium代码修改
1. 修改 `Uranium/` 目录下的代码
2. 重启Cura即可生效

### CuraEngine代码修改
1. 修改 `CuraEngine/` 目录下的代码
2. 重新编译：
   ```bash
   cd CuraEngine
   conan create . --build=missing
   ```
3. 重启Cura

## 故障排除

### 常见问题

1. **Conan版本不兼容**
   - 确保使用Conan 2.6.0
   - 清理缓存：`conan remove "*" -c`

2. **ARM64兼容性问题**
   - 确保使用修改后的依赖版本
   - 检查Python版本是否为3.11.7

3. **依赖包安装失败**
   - 检查网络连接
   - 使用 `--build=missing` 强制构建
   - 清理Conan缓存后重试

### 重置环境

如果需要完全重置：
```bash
# 清理Conan缓存
conan remove "*" -c

# 删除editable包
conan editable remove uranium/5.11.0-alpha.0
conan editable remove curaengine/5.11.0-alpha.0

# 重新开始设置流程
```

## 成功标志

环境搭建成功的标志：
1. `conan editable list` 显示两个editable包
2. `python cura_app.py --help` 能正常运行
3. PyCharm中有可用的运行配置
4. 能够正常启动Cura应用程序

## 注意事项

1. **版本一致性**: 确保所有修改的版本号保持一致
2. **网络要求**: 需要稳定的网络连接下载依赖包
3. **磁盘空间**: 确保有足够的磁盘空间（建议至少10GB）
4. **权限问题**: 在某些系统上可能需要管理员权限

## 后续维护

1. **定期更新**: 定期检查依赖包更新，但要注意ARM64兼容性
2. **备份配置**: 保存修改后的配置文件
3. **文档更新**: 记录任何额外的修改或问题解决方案
