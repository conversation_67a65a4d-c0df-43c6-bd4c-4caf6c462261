@echo off
echo ========================================
echo Cura ARM64 Windows 开发环境自动设置脚本
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "Cura\conanfile.py" (
    echo ❌ 错误: 请在包含Cura、CuraEngine和Uranium文件夹的根目录运行此脚本
    pause
    exit /b 1
)

echo ✅ 目录结构检查通过
echo.

echo 📋 重要信息:
echo - 此脚本已针对ARM64 Windows兼容性进行优化
echo - 将安装Conan 2.6.0 (兼容版本)
echo - 使用修改后的依赖版本解决兼容性问题
echo.

echo 第1步: 安装兼容的Conan版本 (2.6.0)...
pip install "conan>=2.6.0,<2.7.0"
if %errorlevel% neq 0 (
    echo ❌ Conan安装失败
    pause
    exit /b 1
)
echo ✅ Conan 2.6.0 安装成功

echo.
echo 第2步: 配置Conan配置文件...
conan profile detect --force
if %errorlevel% neq 0 (
    echo ❌ Conan配置失败
    pause
    exit /b 1
)
echo ✅ Conan配置完成

echo.
echo 第3步: 添加Uranium为可编辑包...
cd Uranium
conan editable add . --name=uranium --version=5.11.0-alpha.0
if %errorlevel% neq 0 (
    echo ❌ Uranium editable包添加失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Uranium editable包添加成功

echo.
echo 第4步: 添加CuraEngine为可编辑包...
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0-alpha.0
if %errorlevel% neq 0 (
    echo ❌ CuraEngine editable包添加失败
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ CuraEngine editable包添加成功

echo.
echo 第5步: 验证editable包设置...
conan editable list
echo.

echo 第6步: 安装Cura依赖并生成PyCharm配置...
echo 注意: 此步骤可能需要较长时间，请耐心等待...
cd Cura
conan install . -g PyCharmRunEnv --build=missing
if %errorlevel% neq 0 (
    echo ❌ Cura依赖安装失败
    echo 💡 提示: 检查网络连接，确保所有配置文件已正确修改
    cd ..
    pause
    exit /b 1
)
cd ..
echo ✅ Cura依赖安装成功

echo.
echo 🎉 开发环境设置完成!
echo.
echo 📝 后续步骤:
echo 1. 打开PyCharm并导入Cura项目
echo 2. PyCharm运行配置应该自动可用
echo 3. 现在可以并行开发和调试Cura、CuraEngine和Uranium
echo.
echo 🔧 验证安装:
echo   cd Cura
echo   python cura_app.py --help
echo.
echo 🏗️ 单独构建组件:
echo   CuraEngine: cd CuraEngine && conan create . --build=missing
echo   Uranium:    cd Uranium && conan create . --build=missing
echo.
echo 📚 查看完整文档: CURA_DEV_SETUP_COMPLETE_GUIDE.md
echo 📋 快速参考: QUICK_REFERENCE.md
echo.
pause
