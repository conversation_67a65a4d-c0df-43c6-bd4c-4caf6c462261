@echo off
echo Setting up Cura development environment for parallel development...
echo.

REM Check if we're in the correct directory
if not exist "Cura\conanfile.py" (
    echo Error: Please run this script from the root directory containing Cura, CuraEngine, and Uranium folders
    pause
    exit /b 1
)

echo Step 1: Installing Conan 2.6.0 (compatible version)...
pip install "conan>=2.6.0,<2.7.0"

echo.
echo Step 2: Setting up Conan profile...
conan profile detect --force

echo.
echo Step 3: Adding Uranium as editable package...
cd Uranium
conan editable add . --name=uranium --version=5.11.0-alpha.0
if %errorlevel% neq 0 (
    echo Failed to add Uranium as editable package
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo Step 4: Adding CuraEngine as editable package...
cd CuraEngine
conan editable add . --name=curaengine --version=5.11.0-alpha.0
if %errorlevel% neq 0 (
    echo Failed to add CuraEngine as editable package
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo Step 5: Installing Cura dependencies with PyCharm configuration...
cd Cura
conan install . -g PyCharmRunEnv --build=missing -c tools.system.package_manager:mode=install -c tools.system.package_manager:sudo=True
if %errorlevel% neq 0 (
    echo Failed to install Cura dependencies
    cd ..
    pause
    exit /b 1
)
cd ..

echo.
echo Development environment setup completed successfully!
echo.
echo Next steps:
echo 1. Open PyCharm and import the Cura project
echo 2. The PyCharm run configurations should be automatically available
echo 3. You can now develop and debug Cura, CuraEngine, and Uranium in parallel
echo.
echo To build CuraEngine separately:
echo   cd CuraEngine
echo   conan create . --build=missing
echo.
echo To build Uranium separately:
echo   cd Uranium
echo   conan create . --build=missing
echo.
pause
