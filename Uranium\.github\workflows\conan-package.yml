name: conan-package

on:
  push:
    paths:
      - 'plugins/**'
      - 'resources/**'
      - 'UM/**'
      - 'conanfile.py'
      - 'conandata.yml'
      - '.github/workflows/conan-package.yml'
    branches:
      - main
      - 'CURA-*'
      - 'PP-*'
      - 'NP-*'
      - '[0-9].[0-9]*'
      - '[0-9].[0-9][0-9]*'
      -

jobs:
  conan-package:
    uses: ultimaker/cura-workflows/.github/workflows/conan-package.yml@main
    with:
      platform_windows: false
      platform_mac: false
    secrets: inherit
